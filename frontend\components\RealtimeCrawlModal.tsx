import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    Modal,
    Steps,
    Progress,
    Typography,
    Button,
    Alert,
    List,
    Tag,
    Space,
    Spin,
    Card,
    Statistic,
    Row,
    Col
} from 'antd';
import {
    CheckCircleOutlined,
    SyncOutlined,
    TrophyOutlined
} from '@ant-design/icons';
import { api } from '@/services/api';

const { Text } = Typography;
const { Step } = Steps;

interface LogEntry {
    timestamp: string;
    level: 'info' | 'success' | 'warning' | 'error' | 'progress';
    message: string;
    phase: string;
    progress?: number;
}

interface TaskStatus {
    id: string;
    company_name: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    total_steps: number;
    processed_companies: number;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    error_message?: string;
    duration?: number;
}

interface RealtimeCrawlModalProps {
    visible: boolean;
    companyName: string;
    onClose: () => void;
    onComplete: (result: any) => void;
    onCancel: () => void;
}

export const RealtimeCrawlModal: React.FC<RealtimeCrawlModalProps> = ({
    visible,
    companyName,
    onClose,
    onComplete,
    onCancel
}) => {
    const [taskStatus, setTaskStatus] = useState<TaskStatus | null>(null);
    const [logs, setLogs] = useState<LogEntry[]>([]);
    const [currentStep, setCurrentStep] = useState(0);
    const [isStarting, setIsStarting] = useState(false);
    const [socket, setSocket] = useState<WebSocket | null>(null);
    const [estimatedDuration, setEstimatedDuration] = useState(0);
    const [startTime, setStartTime] = useState<Date | null>(null);

    const logsEndRef = useRef<HTMLDivElement>(null);
    const MAX_LOGS = 100; // 限制日志数量，避免内存溢出

    // 自动滚动到日志底部
    const scrollToBottom = () => {
        logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [logs]);

    // 启动实时抓取
    const startRealtimeCrawl = useCallback(async () => {
        if (!companyName) return;

        setIsStarting(true);
        setLogs([]);
        setStartTime(new Date());

        try {
            // 用统一 api 客户端，自动携带 token
            const data: any = await api.post('/equity/start-realtime-crawl', {
                company_name: companyName,
                depth: 2,
                direction: 'both'
            });

            setEstimatedDuration(data?.estimated_duration || 0);

            const taskId = data?.task_id;
            if (!taskId) {
                throw new Error('未能获取到任务ID');
            }
            // 使用 Server-Sent Events 替代 WebSocket
            const ssePath = `/api/equity/sse/crawl-progress/${taskId}`;
            const apiBase = (import.meta as any).env?.VITE_API_URL || '';

            // 获取认证token
            const authStore = await import('@/store/auth');
            const token = authStore.default.getState().token;

            let sseUrl: string;
            if (apiBase) {
                sseUrl = `${apiBase}${ssePath.replace('/api', '')}`;
            } else {
                sseUrl = `${window.location.origin}${ssePath}`;
            }

            // 添加token参数
            if (token) {
                sseUrl += `?token=${encodeURIComponent(token)}`;
            }

            console.log('Connecting to SSE:', sseUrl);
            const eventSource = new EventSource(sseUrl);
            setSocket(eventSource as any); // 复用socket状态

            // 处理 SSE 事件
            eventSource.addEventListener('open', () => {
                addLog('info', '已连接到实时进度服务');
            });

            eventSource.addEventListener('message', (event) => {
                try {
                    const msg = JSON.parse(event.data);
                    switch (msg.type) {
                        case 'connection_established':
                            console.log('SSE连接已确认:', msg.message);
                            break;
                        case 'progress_update':
                            setTaskStatus(prev => prev ? { ...prev, progress: (typeof msg.progress === 'number' ? msg.progress : prev.progress) } : prev);
                            if (typeof msg.progress === 'number') {
                                updateCurrentStep(msg.progress);
                            }
                            break;
                        case 'log_message':
                            addLog(msg.level || 'info', msg.message || '', msg.phase);
                            break;
                        case 'status_change':
                            setTaskStatus(prev => prev ? { ...prev, status: msg.status || prev.status } : prev);
                            if (msg.status === 'completed') {
                                addLog('success', `🎉 分析完成！耗时 ${msg.duration || 0} 秒`);
                                fetchCrawlResult(msg.task_id);
                            } else if (msg.status === 'failed') {
                                addLog('error', `❌ 分析失败: ${msg.error || '未知错误'}`);
                            }
                            break;
                        case 'error':
                            addLog('error', `错误: ${msg.error_message || '未知错误'}`);
                            break;
                        default:
                            console.log('未知消息类型:', msg.type, msg);
                            break;
                    }
                } catch (e) {
                    console.error('SSE消息解析失败:', e);
                }
            });

            eventSource.addEventListener('error', () => {
                addLog('error', 'SSE连接发生错误');
                addLog('warning', '与服务器的连接已断开');
            });

            // 初始状态设置
            setTaskStatus({
                id: data.task_id,
                company_name: companyName,
                status: 'running',
                progress: 0,
                total_steps: 0,
                processed_companies: 0,
                created_at: new Date().toISOString()
            });

            addLog('success', `🚀 开始分析 "${companyName}" 的股权结构`);
            addLog('info', `预计需要 ${data.estimated_duration} 秒`);

        } catch (error) {
            console.error('启动分析失败:', error);
            addLog('error', `启动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        } finally {
            setIsStarting(false);
        }
    }, [companyName]);

    // 获取爬取结果
    const fetchCrawlResult = async (taskId: string) => {
        try {
            const response = await fetch(`/api/equity/crawl-result/${taskId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                onComplete(result.result);
            }
        } catch (error) {
            console.error('获取结果失败:', error);
            addLog('error', '获取分析结果失败');
        }
    };

    // 添加日志
    const addLog = (level: LogEntry['level'], message: string, phase?: string) => {
        const newLog: LogEntry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            phase: phase || '分析中'
        };

        setLogs(prev => {
            const newLogs = [...prev, newLog];
            return newLogs.slice(-MAX_LOGS); // 保持最新的100条日志
        });
    };

    // 更新当前步骤
    const updateCurrentStep = (progress: number) => {
        if (progress < 25) setCurrentStep(0);
        else if (progress < 50) setCurrentStep(1);
        else if (progress < 75) setCurrentStep(2);
        else if (progress < 100) setCurrentStep(3);
        else setCurrentStep(4);
    };

    // 取消任务
    const handleCancel = async () => {
        if (taskStatus?.id) {
            try {
                await api.post(`/equity/cancel-task/${taskStatus.id}`);
                addLog('warning', '任务已取消');
            } catch (error) {
                console.error('取消任务失败:', error);
            }
        }

        if (socket) {
            try {
                if ('close' in socket) socket.close();
                else if ('terminate' in socket) (socket as any).terminate();
            } catch {}
            setSocket(null);
        }

        onCancel();
    };

    // 关闭Modal
    const handleClose = () => {
        if (socket) {
            try {
                if ('close' in socket) socket.close();
                else if ('terminate' in socket) (socket as any).terminate();
            } catch {}
            setSocket(null);
        }
        onClose();
    };

    // 计算已用时间
    const getElapsedTime = () => {
        if (!startTime) return 0;
        return Math.floor((Date.now() - startTime.getTime()) / 1000);
    };

    // 获取日志颜色
    const getLogColor = (level: LogEntry['level']) => {
        const colors = {
            'info': 'default',
            'success': 'success',
            'warning': 'warning',
            'error': 'error',
            'progress': 'processing'
        };
        return colors[level] || 'default';
    };

    // 获取步骤状态
    const getStepStatus = (stepIndex: number) => {
        if (!taskStatus) return 'wait';

        if (taskStatus.status === 'failed' && currentStep >= stepIndex) return 'error';
        if (currentStep > stepIndex) return 'finish';
        if (currentStep === stepIndex) {
            return taskStatus.status === 'running' ? 'process' : 'wait';
        }
        return 'wait';
    };

    // 启动时自动开始抓取
    useEffect(() => {
        if (visible && !taskStatus) {
            startRealtimeCrawl();
        }
    }, [visible, taskStatus, startRealtimeCrawl]);

    // 清理连接
    useEffect(() => {
        return () => {
            if (socket) {
                try {
                    if ('close' in socket) socket.close();
                    else if ('terminate' in socket) (socket as any).terminate();
                } catch {}
            }
        };
    }, [socket]);

    return (
        <Modal
            title={
                <Space>
                    <SyncOutlined spin={taskStatus?.status === 'running'} />
                    <span>实时股权穿透分析</span>
                </Space>
            }
            open={visible}
            onCancel={handleClose}
            width={800}
            footer={[
                <Button key="cancel" onClick={handleCancel} disabled={isStarting}>
                    {taskStatus?.status === 'running' ? '取消任务' : '关闭'}
                </Button>,
                taskStatus?.status === 'completed' && (
                    <Button key="close" type="primary" onClick={handleClose}>
                        查看结果
                    </Button>
                )
            ]}
            maskClosable={false}
        >
            <div style={{ minHeight: '500px' }}>
                {/* 公司信息和状态 */}
                <Card size="small" style={{ marginBottom: 16 }}>
                    <Row gutter={16}>
                        <Col span={8}>
                            <Statistic title="目标公司" value={companyName} />
                        </Col>
                        <Col span={8}>
                            <Statistic
                                title="已用时间"
                                value={getElapsedTime()}
                                suffix="秒"
                                prefix={<SyncOutlined spin={taskStatus?.status === 'running'} />}
                            />
                        </Col>
                        <Col span={8}>
                            <Statistic
                                title="预计时间"
                                value={estimatedDuration}
                                suffix="秒"
                            />
                        </Col>
                    </Row>
                </Card>

                {/* 进度步骤 */}
                <Steps
                    current={currentStep}
                    size="small"
                    style={{ marginBottom: 24 }}
                >
                    <Step
                        title="搜索公司"
                        icon={getStepStatus(0) === 'process' ? <SyncOutlined spin /> : undefined}
                        status={getStepStatus(0)}
                    />
                    <Step
                        title="构建队列"
                        icon={getStepStatus(1) === 'process' ? <SyncOutlined spin /> : undefined}
                        status={getStepStatus(1)}
                    />
                    <Step
                        title="关系分析"
                        icon={getStepStatus(2) === 'process' ? <SyncOutlined spin /> : undefined}
                        status={getStepStatus(2)}
                    />
                    <Step
                        title="数据整理"
                        icon={getStepStatus(3) === 'process' ? <SyncOutlined spin /> : undefined}
                        status={getStepStatus(3)}
                    />
                    <Step
                        title="完成"
                        icon={taskStatus?.status === 'completed' ? <TrophyOutlined /> : undefined}
                        status={getStepStatus(4)}
                    />
                </Steps>

                {/* 进度条 */}
                <Progress
                    percent={taskStatus?.progress || 0}
                    status={
                        taskStatus?.status === 'failed' ? 'exception' :
                        taskStatus?.status === 'completed' ? 'success' :
                        'active'
                    }
                    showInfo={true}
                    style={{ marginBottom: 16 }}
                />

                {/* 统计信息 */}
                {taskStatus && (
                    <Row gutter={16} style={{ marginBottom: 16 }}>
                        <Col span={8}>
                            <Card size="small">
                                <Statistic
                                    title="已处理企业"
                                    value={taskStatus.processed_companies}
                                    prefix={<CheckCircleOutlined />}
                                />
                            </Card>
                        </Col>
                        <Col span={8}>
                            <Card size="small">
                                <Statistic
                                    title="总步骤"
                                    value={taskStatus.total_steps || 0}
                                />
                            </Card>
                        </Col>
                        <Col span={8}>
                            <Card size="small">
                                <Statistic
                                    title="当前状态"
                                    value={
                                        taskStatus.status === 'running' ? '运行中' :
                                        taskStatus.status === 'completed' ? '已完成' :
                                        taskStatus.status === 'failed' ? '失败' :
                                        taskStatus.status === 'pending' ? '等待中' : '未知'
                                    }
                                    valueStyle={{
                                        color: taskStatus.status === 'completed' ? '#3f8600' :
                                               taskStatus.status === 'failed' ? '#cf1322' : '#1890ff'
                                    }}
                                />
                            </Card>
                        </Col>
                    </Row>
                )}

                {/* 实时日志 */}
                <Card
                    title={
                        <Space>
                            <SyncOutlined spin={taskStatus?.status === 'running'} />
                            <span>实时日志</span>
                        </Space>
                    }
                    size="small"
                >
                    <div style={{ height: '200px', overflowY: 'auto', backgroundColor: '#fafafa', padding: '8px' }}>
                        {logs.length === 0 ? (
                            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                                <Spin />
                                <div style={{ marginTop: 8 }}>等待日志信息...</div>
                            </div>
                        ) : (
                            <List
                                size="small"
                                dataSource={logs}
                                rowKey={(log) => `${log.timestamp}-${log.phase}-${(log.message||'').slice(0,24)}`}
                                renderItem={(log) => (
                                    <List.Item style={{ padding: '4px 0', borderBottom: 'none' }}>
                                        <Space size="small" style={{ width: '100%' }}>
                                            <Text type="secondary" style={{ fontSize: '10px', minWidth: '60px' }}>
                                                {new Date(log.timestamp).toLocaleTimeString()}
                                            </Text>
                                            <Tag color={getLogColor(log.level)}>
                                                {log.level.toUpperCase()}
                                            </Tag>
                                            <Text style={{ fontSize: '12px', flex: 1 }}>
                                                {log.message}
                                            </Text>
                                        </Space>
                                    </List.Item>
                                )}
                            />
                        )}
                        <div ref={logsEndRef} />
                    </div>
                </Card>

                {/* 错误信息 */}
                {taskStatus?.error_message && (
                    <Alert
                        message="分析失败"
                        description={taskStatus.error_message}
                        type="error"
                        showIcon
                        style={{ marginTop: 16 }}
                    />
                )}

                {/* 完成提示 */}
                {taskStatus?.status === 'completed' && (
                    <Alert
                        message="分析完成！"
                        description={`股权穿透分析已完成，共耗时 ${taskStatus.duration || 0} 秒。点击"查看结果"按钮查看详细的股权关系图谱。`}
                        type="success"
                        showIcon
                        style={{ marginTop: 16 }}
                    />
                )}
            </div>
        </Modal>
    );
};

export default RealtimeCrawlModal;
